# Concox GPS设备数据处理系统 - SpringBoot详细设计文档

## 1. 项目概述

### 1.1 项目背景
基于现有Node.js的Concox GPS设备数据处理系统，重新设计为SpringBoot架构的企业级应用。系统主要处理GPS设备通过TCP连接发送的位置数据、状态信息和OTA命令。

### 1.2 技术栈选型
- **框架**: Spring Boot 2.7.x
- **数据库**: MongoDB 4.4+
- **缓存**: Redis 6.0+
- **TCP服务**: Netty 4.1.x
- **消息队列**: Spring Integration MQTT
- **构建工具**: Maven 3.8+
- **JDK版本**: OpenJDK 11

### 1.3 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GPS设备群     │────│   TCP服务器     │────│   数据处理层    │
│  (Concox协议)   │    │   (Netty)       │    │  (Spring Boot)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MQTT Broker   │────│   REST API      │────│   数据存储层    │
│  (消息发布)     │    │  (Spring MVC)   │    │ (MongoDB+Redis) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 项目结构设计

### 2.1 Maven项目结构
```
concox-gps-system/
├── pom.xml
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── concox/
│   │   │           ├── ConcoxGpsApplication.java
│   │   │           ├── config/
│   │   │           │   ├── NettyConfig.java
│   │   │           │   ├── MongoConfig.java
│   │   │           │   ├── RedisConfig.java
│   │   │           │   └── MqttConfig.java
│   │   │           ├── controller/
│   │   │           │   ├── DeviceController.java
│   │   │           │   ├── LocationController.java
│   │   │           │   └── OtaController.java
│   │   │           ├── service/
│   │   │           │   ├── DeviceService.java
│   │   │           │   ├── LocationService.java
│   │   │           │   ├── OtaCommandService.java
│   │   │           │   └── ProtocolParserService.java
│   │   │           ├── repository/
│   │   │           │   ├── DeviceRepository.java
│   │   │           │   ├── DeviceStatusRepository.java
│   │   │           │   ├── InvalidDataRepository.java
│   │   │           │   └── OtaCommandRepository.java
│   │   │           ├── entity/
│   │   │           │   ├── Device.java
│   │   │           │   ├── DeviceStatus.java
│   │   │           │   ├── InvalidData.java
│   │   │           │   ├── OtaCommand.java
│   │   │           │   └── GpsLocation.java
│   │   │           ├── dto/
│   │   │           │   ├── DeviceStatusDto.java
│   │   │           │   ├── LocationDto.java
│   │   │           │   └── OtaCommandDto.java
│   │   │           ├── tcp/
│   │   │           │   ├── TcpServer.java
│   │   │           │   ├── TcpChannelHandler.java
│   │   │           │   ├── ImeiManager.java
│   │   │           │   └── protocol/
│   │   │           │       ├── ProtocolParser.java
│   │   │           │       ├── ProtocolFormatter.java
│   │   │           │       ├── CrcCalculator.java
│   │   │           │       └── ProtocolHelper.java
│   │   │           ├── cache/
│   │   │           │   ├── DeviceDataManager.java
│   │   │           │   └── OtaCommandManager.java
│   │   │           ├── mqtt/
│   │   │           │   ├── MqttPublisher.java
│   │   │           │   └── MqttMessageHandler.java
│   │   │           └── exception/
│   │   │               ├── GlobalExceptionHandler.java
│   │   │               ├── DeviceNotFoundException.java
│   │   │               └── ProtocolParseException.java
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       ├── application-prod.yml
│   │       └── logback-spring.xml
│   └── test/
│       └── java/
│           └── com/
│               └── concox/
│                   ├── ConcoxGpsApplicationTests.java
│                   ├── tcp/
│                   │   └── ProtocolParserTest.java
│                   └── service/
│                       └── DeviceServiceTest.java
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
└── README.md
```

## 3. 核心模块详细设计

### 3.1 TCP服务模块

#### 3.1.1 TcpServer.java
```java
@Component
@Slf4j
public class TcpServer {
    
    @Value("${concox.tcp.port:8080}")
    private int tcpPort;
    
    private final TcpChannelHandler channelHandler;
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    
    @PostConstruct
    public void startServer() {
        // Netty服务器启动逻辑
        // 配置Bootstrap和ChannelPipeline
        // 绑定端口并启动监听
    }
    
    @PreDestroy
    public void stopServer() {
        // 优雅关闭服务器
    }
}
```

#### 3.1.2 TcpChannelHandler.java
```java
@Component
@ChannelHandler.Sharable
public class TcpChannelHandler extends ChannelInboundHandlerAdapter {
    
    private final ProtocolParserService protocolParser;
    private final ImeiManager imeiManager;
    private final DeviceService deviceService;
    
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        // 处理接收到的十六进制数据
        // 调用协议解析服务
        // 处理解析结果并响应设备
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        // 异常处理逻辑
    }
}
```

### 3.2 协议解析模块

#### 3.2.1 ProtocolParser.java
```java
@Service
@Slf4j
public class ProtocolParserService {
    
    private final ProtocolFormatter formatter;
    private final CrcCalculator crcCalculator;
    
    public List<ParsedData> parseHexData(String hexData) {
        // 验证数据格式 (7878/7979开头, 0d0a结尾)
        // 分割多个数据包
        // 逐个解析数据包
        // 返回解析结果列表
    }
    
    private ParsedData parsePacket(String packet) {
        // 根据case字段确定数据包类型
        // 调用对应的解析方法
        // 返回结构化数据
    }
}
```

#### 3.2.2 支持的协议类型
- **01**: 登录包 (Login Packet)
- **11**: LBS信息包 (LBS Info)
- **13**: 状态信息包 (Status Info)  
- **16**: GPS+LBS+状态综合包 (Combined Packet)
- **17**: LBS电话号码查询位置包
- **1A**: GPS电话号码查询位置包
- **21**: 在线命令包 (Online Command)
- **22**: GPS位置包 (GPS Location)
- **23**: 心跳包 (Heartbeat)
- **26**: 报警包 (Alarm Packet)
- **8A**: 时间校验包 (Time Check)

### 3.3 数据模型设计

#### 3.3.1 Device.java
```java
@Document(collection = "devices")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Device {
    @Id
    private String id;
    
    @Indexed(unique = true)
    private String imei;
    
    private String model;
    private String client;
    private GpsLocation lastLocation;
    private Double speed;
    private Integer battery;
    private Date createdAt;
    private Date updatedAt;
}
```

#### 3.3.2 DeviceStatus.java
```java
@Document(collection = "status_v2")
@Data
public class DeviceStatus {
    @Id
    private String id;

    private String imei;
    private String hourWindow; // YYYY-MM-DDTHH格式
    private List<StatusEvent> events;

    @Data
    public static class StatusEvent {
        private String tag;
        private String case_;
        private Date time;
        private GpsLocation gps;
        private Integer speed;
        private Integer battery;
        private String device;
        private String client;
        private Date createdAt;
    }
}
```

#### 3.3.3 GpsLocation.java
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GpsLocation {
    private Double latitude;
    private Double longitude;
    private Integer satellites;
    private String course;
    private Date timestamp;
}
```

#### 3.3.4 OtaCommand.java
```java
@Document(collection = "ota_commands")
@Data
public class OtaCommand {
    @Id
    private String id;

    private String imei;
    private String hourWindow;
    private List<OtaEvent> events;

    @Data
    public static class OtaEvent {
        private String content;
        private String contentCode; // ASCII or UTF-16-BE
        private Date time;
        private Date createdAt;
    }
}
```

### 3.4 REST API设计

#### 3.4.1 DeviceController.java
```java
@RestController
@RequestMapping("/concox")
@Slf4j
public class DeviceController {

    private final DeviceService deviceService;
    private final LocationService locationService;

    @GetMapping("/devices")
    public ResponseEntity<List<Device>> getAllDevices() {
        return ResponseEntity.ok(deviceService.findAllDevices());
    }

    @GetMapping("/device/{imei}")
    public ResponseEntity<Device> getDevice(@PathVariable String imei) {
        return deviceService.findByImei(imei)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/last_location/{imei}")
    public ResponseEntity<StatusEvent> getLastLocation(@PathVariable String imei) {
        return locationService.getLastLocation(imei)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.ok(null));
    }

    @PostMapping("/data")
    public ResponseEntity<Void> receiveData(@RequestBody DataRequest request) {
        deviceService.processDeviceData(request.getData());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/invalid_data")
    public ResponseEntity<Void> receiveInvalidData(@RequestBody InvalidDataRequest request) {
        deviceService.saveInvalidData(request);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/invalid_data")
    public ResponseEntity<List<InvalidData>> getInvalidData(
            @RequestParam(defaultValue = "10") String limit) {
        return ResponseEntity.ok(deviceService.getInvalidData(limit));
    }
}
```

#### 3.4.2 OtaController.java
```java
@RestController
@RequestMapping("/concox")
@Slf4j
public class OtaController {

    private final OtaCommandService otaCommandService;

    @PostMapping("/ota_command")
    public ResponseEntity<Void> receiveOtaCommand(@RequestBody OtaCommandRequest request) {
        otaCommandService.saveOtaCommand(request.getData());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/send_ota/{imei}")
    public ResponseEntity<Void> sendOtaCommand(
            @PathVariable String imei,
            @RequestBody SendOtaRequest request) {
        otaCommandService.queueOtaCommand(imei, request.getMessage());
        return ResponseEntity.ok().build();
    }
}
```

### 3.5 服务层设计

#### 3.5.1 DeviceService.java
```java
@Service
@Transactional
@Slf4j
public class DeviceService {

    private final DeviceRepository deviceRepository;
    private final DeviceStatusRepository statusRepository;
    private final InvalidDataRepository invalidDataRepository;
    private final DeviceDataManager dataManager;
    private final MqttPublisher mqttPublisher;

    public List<Device> findAllDevices() {
        return deviceRepository.findAll();
    }

    public Optional<Device> findByImei(String imei) {
        return deviceRepository.findByImei(imei);
    }

    public void processDeviceData(List<StatusEvent> events) {
        if (events.isEmpty()) return;

        String imei = events.get(0).getImei();
        String hourWindow = getCurrentHourWindow();

        // 更新设备缓存
        if (events.get(0).getGps() != null) {
            dataManager.updateDeviceCache(imei, events.get(0));
        }

        // 保存到MongoDB
        statusRepository.upsertEvents(imei, hourWindow, events);

        // 发布MQTT消息
        Device device = findByImei(imei).orElse(null);
        if (device != null && device.getClient() != null) {
            mqttPublisher.publishDeviceData(device.getClient(), events);
        }

        log.info("Processed {} events for device {}", events.size(), imei);
    }

    public void saveInvalidData(InvalidDataRequest request) {
        InvalidData invalidData = new InvalidData();
        invalidData.setData(request.getData());
        invalidData.setTimestamp(new Date());
        invalidDataRepository.save(invalidData);
    }

    public List<InvalidData> getInvalidData(String limit) {
        if ("all".equals(limit)) {
            return invalidDataRepository.findAll();
        }
        return invalidDataRepository.findTopN(Integer.parseInt(limit));
    }

    private String getCurrentHourWindow() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH"));
    }
}
```

#### 3.5.2 LocationService.java
```java
@Service
@Slf4j
public class LocationService {

    private final DeviceStatusRepository statusRepository;

    public Optional<StatusEvent> getLastLocation(String imei) {
        return statusRepository.findLastLocationByImei(imei);
    }

    public List<StatusEvent> getLocationHistory(String imei, Date startTime, Date endTime) {
        return statusRepository.findLocationHistoryByImeiAndTimeRange(imei, startTime, endTime);
    }
}
```

### 3.6 数据访问层设计

#### 3.6.1 DeviceRepository.java
```java
@Repository
public interface DeviceRepository extends MongoRepository<Device, String> {

    Optional<Device> findByImei(String imei);

    List<Device> findByClient(String client);

    @Query("{'lastLocation': {$exists: true}}")
    List<Device> findDevicesWithLocation();
}
```

#### 3.6.2 DeviceStatusRepository.java
```java
@Repository
public interface DeviceStatusRepository extends MongoRepository<DeviceStatus, String> {

    @Query("{'imei': ?0, 'events': {$slice: -1}}")
    Optional<DeviceStatus> findLastEventByImei(String imei);

    @Query("{'imei': ?0, 'events.gps': {$exists: true}, 'events': {$slice: -1}}")
    Optional<DeviceStatus> findLastLocationByImei(String imei);

    @Aggregation(pipeline = {
        "{ $match: { 'imei': ?0 } }",
        "{ $unwind: '$events' }",
        "{ $match: { 'events.time': { $gte: ?1, $lte: ?2 } } }",
        "{ $sort: { 'events.time': -1 } }",
        "{ $limit: 100 }"
    })
    List<StatusEvent> findLocationHistoryByImeiAndTimeRange(String imei, Date startTime, Date endTime);
}
```

### 3.7 缓存管理设计

#### 3.7.1 DeviceDataManager.java
```java
@Service
@Slf4j
public class DeviceDataManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private final DeviceRepository deviceRepository;

    public void updateDeviceCache(String imei, StatusEvent event) {
        try {
            String key = "device:" + imei;
            DeviceCacheData cacheData = getDeviceCache(imei);

            if (cacheData == null) {
                cacheData = new DeviceCacheData();
                cacheData.setImei(imei);
            }

            // 更新缓存数据
            if (event.getGps() != null) {
                cacheData.setLastLocation(event.getGps());
            }
            if (event.getSpeed() != null) {
                cacheData.setSpeed(event.getSpeed());
            }
            if (event.getBattery() != null) {
                cacheData.setBattery(event.getBattery());
            }
            cacheData.setLastUpdateTime(new Date());

            redisTemplate.opsForValue().set(key, cacheData, Duration.ofHours(24));
            log.debug("Updated cache for device: {}", imei);

        } catch (Exception e) {
            log.error("Failed to update device cache for {}: {}", imei, e.getMessage());
        }
    }

    public DeviceCacheData getDeviceCache(String imei) {
        try {
            String key = "device:" + imei;
            return (DeviceCacheData) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Failed to get device cache for {}: {}", imei, e.getMessage());
            return null;
        }
    }

    public DeviceInfo getDeviceInfo(String imei) {
        // 先从缓存获取
        DeviceCacheData cacheData = getDeviceCache(imei);

        // 从数据库获取设备基本信息
        Device device = deviceRepository.findByImei(imei).orElse(null);

        // 合并数据
        DeviceInfo deviceInfo = new DeviceInfo();
        if (device != null) {
            deviceInfo.setId(device.getId());
            deviceInfo.setImei(device.getImei());
            deviceInfo.setClient(device.getClient());
        }

        if (cacheData != null) {
            deviceInfo.setLastLocation(cacheData.getLastLocation());
            deviceInfo.setSpeed(cacheData.getSpeed());
            deviceInfo.setBattery(cacheData.getBattery());
        }

        return deviceInfo;
    }
}
```

### 3.8 MQTT集成设计

#### 3.8.1 MqttPublisher.java
```java
@Service
@Slf4j
public class MqttPublisher {

    private final MqttTemplate mqttTemplate;

    @Value("${concox.mqtt.topic.prefix:concox/device}")
    private String topicPrefix;

    public void publishDeviceData(String client, List<StatusEvent> events) {
        try {
            String topic = topicPrefix + "/" + client;
            String payload = JsonUtils.toJson(events);

            mqttTemplate.convertAndSend(topic, payload);
            log.debug("Published {} events to topic: {}", events.size(), topic);

        } catch (Exception e) {
            log.error("Failed to publish MQTT message for client {}: {}", client, e.getMessage());
        }
    }

    public void publishDeviceStatus(String imei, String status) {
        try {
            String topic = topicPrefix + "/" + imei + "/status";
            mqttTemplate.convertAndSend(topic, status);

        } catch (Exception e) {
            log.error("Failed to publish device status for {}: {}", imei, e.getMessage());
        }
    }
}
```

### 3.9 配置文件设计

#### 3.9.1 application.yml
```yaml
server:
  port: 8080

spring:
  application:
    name: concox-gps-system

  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/concox}
      database: ${MONGODB_DATABASE:concox}

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

concox:
  tcp:
    port: ${TCP_PORT:8081}
    boss-threads: 1
    worker-threads: 4
    so-backlog: 128
    so-keepalive: true
    socket-timeout: 1800000  # 30分钟

  mqtt:
    broker-url: ${MQTT_BROKER_URL:tcp://localhost:1883}
    client-id: ${MQTT_CLIENT_ID:concox-gps-system}
    username: ${MQTT_USERNAME:}
    password: ${MQTT_PASSWORD:}
    topic:
      prefix: ${MQTT_TOPIC_PREFIX:concox/device}

  api:
    port: ${API_PORT:8080}

logging:
  level:
    com.concox: DEBUG
    org.springframework.data.mongodb: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/concox-gps-system.log
    max-size: 100MB
    max-history: 30
```

## 4. 部署配置

### 4.1 Docker配置

#### 4.1.1 Dockerfile
```dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

COPY target/concox-gps-system-*.jar app.jar

EXPOSE 8080 8081

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 4.1.2 docker-compose.yml
```yaml
version: '3.8'

services:
  concox-app:
    build: .
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/concox
      - REDIS_HOST=redis
      - MQTT_BROKER_URL=tcp://mqtt:1883
    depends_on:
      - mongo
      - redis
      - mqtt
    restart: unless-stopped

  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  mqtt:
    image: eclipse-mosquitto:2.0
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - mqtt_data:/mosquitto/data
      - mqtt_logs:/mosquitto/log
    restart: unless-stopped

volumes:
  mongo_data:
  redis_data:
  mqtt_data:
  mqtt_logs:
```

## 5. 测试策略

### 5.1 单元测试
- 协议解析器测试
- 服务层业务逻辑测试
- 数据访问层测试

### 5.2 集成测试
- TCP服务器连接测试
- API接口测试
- 数据库集成测试

### 5.3 性能测试
- 并发连接压力测试
- 数据处理性能测试
- 内存和CPU使用率监控

## 6. 监控和运维

### 6.1 健康检查
- Spring Boot Actuator端点
- 数据库连接状态检查
- Redis连接状态检查
- MQTT连接状态检查

### 6.2 日志管理
- 结构化日志输出
- 日志级别动态调整
- 日志文件轮转策略

### 6.3 指标监控
- JVM性能指标
- 业务指标（设备连接数、数据处理量）
- 错误率和响应时间监控

## 7. 安全考虑

### 7.1 网络安全
- TCP端口访问控制
- API接口认证授权
- 数据传输加密

### 7.2 数据安全
- 敏感数据脱敏
- 数据备份策略
- 访问日志审计

## 8. 扩展性设计

### 8.1 水平扩展
- 无状态服务设计
- 负载均衡配置
- 数据库分片策略

### 8.2 功能扩展
- 插件化协议解析
- 多种设备类型支持
- 实时数据分析功能

## 9. 详细技术实现

### 9.1 Maven依赖配置 (pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.concox</groupId>
    <artifactId>concox-gps-system</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>Concox GPS System</name>
    <description>GPS设备数据处理系统</description>

    <properties>
        <java.version>11</java.version>
        <spring-boot.version>2.7.14</spring-boot.version>
        <netty.version>4.1.94.Final</netty.version>
        <mongodb.version>4.10.2</mongodb.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Netty for TCP Server -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <!-- JSON Processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>de.flapdoodle.embed</groupId>
            <artifactId>de.flapdoodle.embed.mongo</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>0.7.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### 9.2 核心配置类详细实现

#### 9.2.1 NettyConfig.java
```java
@Configuration
@EnableConfigurationProperties(NettyProperties.class)
@Slf4j
public class NettyConfig {

    @Bean
    public EventLoopGroup bossGroup() {
        return new NioEventLoopGroup(1);
    }

    @Bean
    public EventLoopGroup workerGroup(NettyProperties properties) {
        return new NioEventLoopGroup(properties.getWorkerThreads());
    }

    @Bean
    public ServerBootstrap serverBootstrap(EventLoopGroup bossGroup,
                                         EventLoopGroup workerGroup,
                                         TcpChannelHandler channelHandler,
                                         NettyProperties properties) {
        return new ServerBootstrap()
                .group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, properties.getSoBacklog())
                .childOption(ChannelOption.SO_KEEPALIVE, properties.isSoKeepalive())
                .childOption(ChannelOption.TCP_NODELAY, true)
                .childOption(ChannelOption.SO_TIMEOUT, properties.getSocketTimeout())
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ChannelPipeline pipeline = ch.pipeline();

                        // 添加编解码器
                        pipeline.addLast("decoder", new StringDecoder(CharsetUtil.UTF_8));
                        pipeline.addLast("encoder", new StringEncoder(CharsetUtil.UTF_8));

                        // 添加空闲状态处理器
                        pipeline.addLast("idleStateHandler",
                            new IdleStateHandler(0, 0, properties.getSocketTimeout() / 1000));

                        // 添加业务处理器
                        pipeline.addLast("handler", channelHandler);
                    }
                });
    }

    @ConfigurationProperties(prefix = "concox.tcp")
    @Data
    public static class NettyProperties {
        private int port = 8081;
        private int bossThreads = 1;
        private int workerThreads = 4;
        private int soBacklog = 128;
        private boolean soKeepalive = true;
        private int socketTimeout = 1800000; // 30分钟
    }
}
```

#### 9.2.2 MongoConfig.java
```java
@Configuration
@EnableMongoRepositories(basePackages = "com.concox.repository")
public class MongoConfig {

    @Bean
    public MongoCustomConversions mongoCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new DateToStringConverter());
        converters.add(new StringToDateConverter());
        return new MongoCustomConversions(converters);
    }

    @Bean
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDbFactory) {
        MongoTemplate template = new MongoTemplate(mongoDbFactory);
        template.setWriteConcern(WriteConcern.ACKNOWLEDGED);
        return template;
    }

    // 自定义转换器
    @WritingConverter
    public static class DateToStringConverter implements Converter<Date, String> {
        @Override
        public String convert(Date source) {
            return source != null ? source.toInstant().toString() : null;
        }
    }

    @ReadingConverter
    public static class StringToDateConverter implements Converter<String, Date> {
        @Override
        public Date convert(String source) {
            return source != null ? Date.from(Instant.parse(source)) : null;
        }
    }
}
```

#### 9.2.3 RedisConfig.java
```java
@Configuration
@EnableCaching
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,
                                          ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(objectMapper);

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new Jackson2JsonRedisSerializer<>(Object.class)));

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .build();
    }
}
```

### 9.3 协议解析详细实现

#### 9.3.1 ProtocolHelper.java
```java
@Component
@Slf4j
public class ProtocolHelper {

    // CRC16校验表
    private static final int[] CRC_TABLE = {
        0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
        // ... 完整的CRC16表
    };

    /**
     * 计算CRC16校验码
     */
    public String calculateCrc16(String data) {
        int crc = 0xFFFF;
        for (int i = 0; i < data.length(); i += 2) {
            int value = Integer.parseInt(data.substring(i, i + 2), 16);
            crc = (crc >> 8) ^ CRC_TABLE[(crc ^ value) & 0xFF];
        }
        return String.format("%04x", crc ^ 0xFFFF);
    }

    /**
     * 解析日期时间
     */
    public Date parseDateTime(String hexDateTime) {
        try {
            int year = Integer.parseInt(hexDateTime.substring(0, 2), 16) + 2000;
            int month = Integer.parseInt(hexDateTime.substring(2, 4), 16);
            int day = Integer.parseInt(hexDateTime.substring(4, 6), 16);
            int hour = Integer.parseInt(hexDateTime.substring(6, 8), 16);
            int minute = Integer.parseInt(hexDateTime.substring(8, 10), 16);
            int second = Integer.parseInt(hexDateTime.substring(10, 12), 16);

            return Date.from(LocalDateTime.of(year, month, day, hour, minute, second)
                    .atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            log.error("Failed to parse date time: {}", hexDateTime, e);
            return new Date();
        }
    }

    /**
     * 解析GPS坐标
     */
    public GpsLocation parseGpsLocation(String hexGps) {
        try {
            // 纬度解析 (4字节)
            long latRaw = Long.parseLong(hexGps.substring(0, 8), 16);
            double latitude = latRaw / 1800000.0;

            // 经度解析 (4字节)
            long lngRaw = Long.parseLong(hexGps.substring(8, 16), 16);
            double longitude = lngRaw / 1800000.0;

            return new GpsLocation(latitude, longitude, null, null, new Date());
        } catch (Exception e) {
            log.error("Failed to parse GPS location: {}", hexGps, e);
            return null;
        }
    }

    /**
     * 解析电压信息
     */
    public double parseVoltage(String hexVoltage) {
        try {
            int voltage = Integer.parseInt(hexVoltage, 16);
            return voltage / 100.0; // 转换为伏特
        } catch (Exception e) {
            log.error("Failed to parse voltage: {}", hexVoltage, e);
            return 0.0;
        }
    }

    /**
     * 计算电池百分比
     */
    public int calculateBatteryPercentage(double voltage) {
        // 基于电压计算电池百分比的算法
        if (voltage >= 4.0) return 100;
        if (voltage >= 3.8) return 80;
        if (voltage >= 3.6) return 60;
        if (voltage >= 3.4) return 40;
        if (voltage >= 3.2) return 20;
        return 10;
    }

    /**
     * 解析GSM信号强度
     */
    public int parseGsmStrength(String hexGsm) {
        try {
            return Integer.parseInt(hexGsm, 16);
        } catch (Exception e) {
            log.error("Failed to parse GSM strength: {}", hexGsm, e);
            return 0;
        }
    }

    /**
     * 十六进制转ASCII
     */
    public String hexToAscii(String hex) {
        StringBuilder ascii = new StringBuilder();
        for (int i = 0; i < hex.length(); i += 2) {
            String hexPair = hex.substring(i, i + 2);
            int value = Integer.parseInt(hexPair, 16);
            ascii.append((char) value);
        }
        return ascii.toString();
    }

    /**
     * ASCII转十六进制
     */
    public String asciiToHex(String ascii) {
        StringBuilder hex = new StringBuilder();
        for (char c : ascii.toCharArray()) {
            hex.append(String.format("%02x", (int) c));
        }
        return hex.toString();
    }

    /**
     * 添加协议头尾
     */
    public String wrapProtocolData(String data) {
        return "7878" + data + "0d0a";
    }

    /**
     * 验证协议格式
     */
    public boolean isValidProtocolData(String data) {
        if (data == null || data.length() < 8) {
            return false;
        }

        String lowerData = data.toLowerCase();
        return (lowerData.startsWith("7878") || lowerData.startsWith("7979"))
               && lowerData.endsWith("0d0a");
    }
}
```

### 9.4 异常处理和监控

#### 9.4.1 GlobalExceptionHandler.java
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(DeviceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleDeviceNotFound(DeviceNotFoundException e) {
        log.warn("Device not found: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new ErrorResponse("DEVICE_NOT_FOUND", e.getMessage()));
    }

    @ExceptionHandler(ProtocolParseException.class)
    public ResponseEntity<ErrorResponse> handleProtocolParseError(ProtocolParseException e) {
        log.error("Protocol parse error: {}", e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new ErrorResponse("PROTOCOL_PARSE_ERROR", e.getMessage()));
    }

    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ErrorResponse> handleDataAccessError(DataAccessException e) {
        log.error("Database access error: {}", e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("DATABASE_ERROR", "数据库访问错误"));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericError(Exception e) {
        log.error("Unexpected error: {}", e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("INTERNAL_ERROR", "系统内部错误"));
    }

    @Data
    @AllArgsConstructor
    public static class ErrorResponse {
        private String code;
        private String message;
        private LocalDateTime timestamp = LocalDateTime.now();

        public ErrorResponse(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }
}
```

#### 9.4.2 自定义异常类
```java
public class DeviceNotFoundException extends RuntimeException {
    public DeviceNotFoundException(String imei) {
        super("Device not found with IMEI: " + imei);
    }
}

public class ProtocolParseException extends RuntimeException {
    public ProtocolParseException(String message, Throwable cause) {
        super(message, cause);
    }

    public ProtocolParseException(String message) {
        super(message);
    }
}
```

### 9.5 性能优化和监控指标

#### 9.5.1 自定义Actuator端点
```java
@Component
@Endpoint(id = "concox-metrics")
public class ConcoxMetricsEndpoint {

    private final AtomicLong connectedDevices = new AtomicLong(0);
    private final AtomicLong processedMessages = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);

    @ReadOperation
    public Map<String, Object> metrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("connectedDevices", connectedDevices.get());
        metrics.put("processedMessages", processedMessages.get());
        metrics.put("errorCount", errorCount.get());
        metrics.put("timestamp", Instant.now());
        return metrics;
    }

    public void incrementConnectedDevices() {
        connectedDevices.incrementAndGet();
    }

    public void decrementConnectedDevices() {
        connectedDevices.decrementAndGet();
    }

    public void incrementProcessedMessages() {
        processedMessages.incrementAndGet();
    }

    public void incrementErrorCount() {
        errorCount.incrementAndGet();
    }
}
```

#### 9.5.2 性能监控配置
```java
@Configuration
@EnableScheduling
public class MonitoringConfig {

    @Bean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }

    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }
}
```

## 10. 数据库索引优化

### 10.1 MongoDB索引策略
```javascript
// 设备集合索引
db.devices.createIndex({ "imei": 1 }, { unique: true })
db.devices.createIndex({ "client": 1 })
db.devices.createIndex({ "lastLocation": 1 })

// 设备状态集合索引
db.status_v2.createIndex({ "imei": 1, "hourWindow": 1 }, { unique: true })
db.status_v2.createIndex({ "imei": 1, "events.time": -1 })
db.status_v2.createIndex({ "events.gps": 1 })

// OTA命令集合索引
db.ota_commands.createIndex({ "imei": 1, "hourWindow": 1 }, { unique: true })
db.ota_commands.createIndex({ "imei": 1, "events.time": -1 })

// 无效数据集合索引
db.invalid_status.createIndex({ "timestamp": -1 })
```

### 10.2 数据分片策略
```javascript
// 基于IMEI的分片键配置
sh.shardCollection("concox.status_v2", { "imei": "hashed" })
sh.shardCollection("concox.ota_commands", { "imei": "hashed" })
```

## 11. 安全增强

### 11.1 API安全配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/concox/**").hasRole("API_USER")
                .anyRequest().authenticated()
            )
            .httpBasic();
        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

### 11.2 数据脱敏配置
```java
@JsonSerialize(using = ImeiMaskingSerializer.class)
public class ImeiMaskingSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if (value != null && value.length() > 4) {
            String masked = value.substring(0, 4) + "****" + value.substring(value.length() - 4);
            gen.writeString(masked);
        } else {
            gen.writeString(value);
        }
    }
}
```
```
