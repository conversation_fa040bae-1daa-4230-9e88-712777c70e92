{"name": "concox", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pm2 start ecosystem.config.js --env production --wait-ready"}, "author": "", "license": "ISC", "dependencies": {"@intugine-technologies/mongodb": "^1.12.5", "axios": "^0.19.0", "bluebird": "^3.7.2", "body-parser": "^1.19.0", "event-loop-inspector": "^1.2.2", "express": "^4.17.1", "moment": "^2.24.0", "mqtt": "^3.0.0", "redis": "^3.0.2"}}